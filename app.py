from flask import Flask, render_template, request, redirect, flash
from algosdk import mnemonic, account
from algosdk.v2client import algod
from algosdk.transaction import PaymentTxn, wait_for_confirmation
import json
import base64
import os
import requests
from dotenv import load_dotenv

load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv('FLASK_SECRET_KEY', 'fallback-secret-for-dev-only')  # For flash messages

ALGOD_ADDRESS = "https://testnet-api.algonode.cloud"
ALGOD_TOKEN = ""  # No token needed for Algonode

# Load client
algod_client = algod.AlgodClient(ALGOD_TOKEN, ALGOD_ADDRESS)

# Optional Improvements.
# Add pagination for large numbers of songs
# Add caching to reduce API calls
# Add transaction ID links to the explorer in the UI
# Note: For v3, we're not using smart contracts - just payment transactions with JSON notes

# Load your 25-word mnemonic from .env
mnemonic_phrase = os.getenv("ALGOWALLET_MNEMONIC")
private_key = mnemonic.to_private_key(mnemonic_phrase)
sender_address = account.address_from_private_key(private_key)


@app.route("/")
def index():
    """View all registered songs from payment transaction notes"""
    songs = []

    try:
        # Use Algorand Indexer API to get transactions more efficiently
        # This is a public indexer endpoint for testnet

        # Get transactions from our address with notes
        indexer_url = "https://testnet-idx.algonode.cloud"

        # Query for payment transactions from our address
        url = f"{indexer_url}/v2/transactions"
        params = {
            'address': sender_address,
            'tx-type': 'pay',
            'limit': 100  # Get last 100 transactions
        }

        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            transactions = data.get('transactions', [])

            for txn in transactions:
                # Check if this is a zero-value self-payment with a note
                if (txn.get('sender') == sender_address and
                    txn.get('payment-transaction', {}).get('receiver') == sender_address and
                    txn.get('payment-transaction', {}).get('amount') == 0 and
                    'note' in txn):

                    try:
                        # Decode the note and parse JSON
                        note_bytes = base64.b64decode(txn['note'])
                        note_str = note_bytes.decode('utf-8')
                        song_data = json.loads(note_str)

                        # Check if this is a song registry transaction
                        if (song_data.get('app') == 'songReg' and
                            song_data.get('v') == 3):

                            song = {
                                'id': len(songs) + 1,  # Simple incrementing ID
                                'title': song_data.get('t', 'Unknown'),
                                'url': song_data.get('u', ''),
                                'price': song_data.get('p', 0),
                                'owner': song_data.get('owner', 'Unknown'),
                                'tx_id': txn.get('id', '')
                            }
                            songs.append(song)

                    except (json.JSONDecodeError, UnicodeDecodeError, KeyError):
                        # Skip transactions with invalid JSON in notes
                        continue
        else:
            flash("Unable to retrieve transaction history from indexer", "warning")

    except Exception as e:
        flash(f"Error retrieving songs: {e}", "error")

    # Sort songs by most recent first (reverse chronological order)
    songs.reverse()

    return render_template("index.html", songs=songs)


@app.route("/register_song", methods=["POST"])
def register_song():
    try:
        title = request.form.get("title")
        url = request.form.get("url")
        price = int(request.form.get("price"))

        # Build compact JSON for the note
        song_obj = {
            "app": "songReg",
            "v": 3,  # Version 3 of song registry
            "t": title,
            "u": url,
            "p": price,
            "owner": sender_address  # Include owner in the JSON
        }
        song_json = json.dumps(song_obj, separators=(",", ":")).encode()  # compact bytes

        params = algod_client.suggested_params()

        # Create a 0-ALGO payment txn that stores the song data in the note field
        txn = PaymentTxn(
            sender=sender_address,
            sp=params,
            receiver=sender_address,  # self-pay (no money moves)
            amt=0,  # Zero value payment
            note=song_json  # Song data stored in transaction note
        )

        # Sign and send the transaction
        signed_txn = txn.sign(private_key)
        tx_id = algod_client.send_raw_transaction(signed_txn)
        wait_for_confirmation(algod_client, tx_id, 4)

        flash(f"Song registered successfully! Transaction ID: {tx_id}", "success")

    except Exception as e:
        flash(f"Error registering song: {e}", "error")

    return redirect("/")    # back to the landing page


if __name__ == "__main__":
    app.run(debug=False, host='0.0.0.0', port=int(os.environ.get('PORT', 5000)))
